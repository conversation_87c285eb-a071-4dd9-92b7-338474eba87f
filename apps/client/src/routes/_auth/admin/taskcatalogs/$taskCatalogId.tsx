import type { Project, Schema, Task } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute, useNavigate } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import { DataTable } from '~/components/datatable'
import { TaskCatalogEditor } from '~/components/task-catalog-editor'
import { TaskEditor } from '~/components/task-editor'
import { TaskPicker } from '~/components/task-picker'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import {
  useTaskCatalogManagement,
  useTaskManagement,
} from '~/modules/tasks/task-hooks'

export const Route = createFileRoute(
  '/_auth/admin/taskcatalogs/$taskCatalogId'
)({
  component: TaskCatalogDetailPage,
})

function TaskCatalogDetailPage() {
  const { taskCatalogId } = Route.useParams()
  const z = useZero<Schema>()
  const navigate = useNavigate()
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [isTaskEditorOpen, setIsTaskEditorOpen] = useState(false)
  const [isLinkTaskDialogOpen, setIsLinkTaskDialogOpen] = useState(false)
  const [selectedTaskToLink, setSelectedTaskToLink] = useState<string | null>(
    null
  )

  // Fetch the task catalog
  const [taskCatalogResult = null] = useQuery(
    z.query.taskCatalogs
      .where('id', '=', taskCatalogId)
      .related('projectCatalog')
      .related('taskToTaskCatalogs', (q) =>
        q.related('task', (q) => q.related('project'))
      )
      .one(),
    { ttl: 'forever' }
  )

  const taskCatalog = taskCatalogResult ?? null
  const linkedTasksRelations = taskCatalog?.taskToTaskCatalogs ?? []

  // Extract tasks from the relations
  const linkedTasks = useMemo(() => {
    return linkedTasksRelations
      .filter((relation) => relation.task)
      .map((relation) => relation.task as Task & { project?: Project })
  }, [linkedTasksRelations])

  // Get the selected task
  const selectedTask =
    linkedTasks.find((task) => task.id === selectedTaskId) || null

  // Use the task catalog management hook
  const {
    handleSave: handleSaveTaskCatalog,
    handleDelete: handleDeleteTaskCatalog,
  } = useTaskCatalogManagement(z, taskCatalog ? [taskCatalog] : [])

  // Use the task management hook
  const { handleDelete: handleDeleteTaskFromHook } = useTaskManagement(
    z,
    linkedTasks
  )

  const [editorOpen, setEditorOpen] = useState(false)
  const handleEditTaskCatalog = () => {
    setEditorOpen(true)
  }
  // Handle row click
  const handleRowClick = (task: Task) => {
    setSelectedTaskId(task.id)
  }

  // Handle row double click
  const handleRowDoubleClick = (task: Task) => {
    setSelectedTaskId(task.id)
    setIsTaskEditorOpen(true)
  }

  // Handle add new task
  const handleAddNewTask = () => {
    setSelectedTaskId(null)
    setIsTaskEditorOpen(true)
  }

  // Handle link existing task
  const handleLinkExistingTask = () => {
    setSelectedTaskToLink(null)
    setIsLinkTaskDialogOpen(true)
  }

  // Handle save task
  const handleSaveTask = async (task: Task) => {
    const taskExists = linkedTasks.some((t) => t.id === task.id)
    doSaveTask(task, taskCatalogId, taskExists, z)
  }

  // Handle link task
  const handleLinkTask = async () => {
    if (!selectedTaskToLink) return

    try {
      // Check if the task is already linked
      if (linkedTasks.some((t) => t.id === selectedTaskToLink)) {
        console.log('Task is already linked to this task catalog')
        return
      }

      // Create task to task catalog relation
      await z.mutate.taskToTaskCatalogs.insert({
        taskId: selectedTaskToLink,
        taskCatalogId,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: z.userID,
        updatedBy: z.userID,
      })

      // Close the dialog
      setIsLinkTaskDialogOpen(false)
    } catch (error) {
      console.error('Error linking task:', error)
    }
  }

  // Handle delete task - use the hook
  const handleDeleteTask = handleDeleteTaskFromHook

  // Handle unlink task (remove the relation without deleting the task)
  const handleUnlinkTask = async (taskId: string) => {
    try {
      // Delete the task to task catalog relation
      await z.mutate.taskToTaskCatalogs.delete({
        taskId,
        taskCatalogId,
      })
    } catch (error) {
      console.error('Error unlinking task:', error)
    }
  }

  // Define columns for the tasks table
  const columns: ColumnDef<Task & { project?: Project }>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'project.name',
      header: 'Project',
      cell: ({ row }) => row.original.project?.name || '-',
    },
    {
      accessorKey: 'status',
      header: 'Status',
    },
    {
      accessorKey: 'lastUsed',
      header: 'Last Used',
      cell: ({ row }) =>
        row.original.lastUsed
          ? new Date(row.original.lastUsed).toLocaleString()
          : '-',
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant="destructive"
          size="sm"
          onClick={(e) => {
            e.stopPropagation() // Prevent row click
            handleUnlinkTask(row.original.id)
          }}
        >
          Unlink
        </Button>
      ),
    },
  ]

  // Handle back button click
  const handleBackClick = () => {
    navigate({
      to: '/admin/taskcatalogs',
      search: { projectCatalogId: taskCatalog?.projectCatalogId ?? undefined },
    })
  }

  if (!taskCatalog) {
    return <div>Loading...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleBackClick}>
            Back
          </Button>
          <h1 className="text-2xl font-bold">{taskCatalog?.name}</h1>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleEditTaskCatalog}>
            Edit Task Catalog
          </Button>
          <Button variant="outline" onClick={handleLinkExistingTask}>
            Link Existing Task
          </Button>
          <Button onClick={handleAddNewTask}>Add New Task</Button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 p-4 border rounded-md">
        <div>
          <p className="text-sm font-medium">Key:</p>
          <p>{taskCatalog?.key || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Project Catalog:</p>
          <p>{taskCatalog?.projectCatalog?.name || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Status:</p>
          <p>{taskCatalog?.status || '-'}</p>
        </div>
        <div>
          <p className="text-sm font-medium">Remote ID:</p>
          <p>{taskCatalog?.remoteId || '-'}</p>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-2">Linked Tasks</h2>
        <div className="border rounded-md">
          <DataTable
            columns={columns}
            data={linkedTasks}
            onRowClick={handleRowClick}
            onRowDoubleClick={handleRowDoubleClick}
            selectedRowId={selectedTaskId || undefined}
          />
        </div>
      </div>

      <TaskEditor
        task={selectedTask}
        taskCatalogId={taskCatalogId}
        isOpen={isTaskEditorOpen}
        onClose={() => setIsTaskEditorOpen(false)}
        onSave={handleSaveTask}
        onDelete={handleDeleteTask}
      />

      {editorOpen && (
        <TaskCatalogEditor
          taskCatalog={taskCatalog}
          isOpen={true}
          onClose={() => setEditorOpen(false)}
          onSave={handleSaveTaskCatalog}
          onDelete={handleDeleteTaskCatalog}
        />
      )}

      {/* Link Existing Task Dialog */}
      <Dialog
        open={isLinkTaskDialogOpen}
        onOpenChange={(open) => !open && setIsLinkTaskDialogOpen(false)}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Link Existing Task</DialogTitle>
            <DialogDescription>
              Select an existing task to link to this task catalog.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <TaskPicker
              value={selectedTaskToLink || undefined}
              onChange={setSelectedTaskToLink}
              display="combobox"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsLinkTaskDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleLinkTask} disabled={!selectedTaskToLink}>
              Link Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

async function doSaveTask(
  task: Task,
  taskCatalogId: string,
  taskExists: boolean,
  z: Zero<Schema>
) {
  try {
    await z.mutateBatch(async (tx) => {
      const taskId = task.id || uuidv7()
      if (taskExists) {
        // Update existing task
        await tx.tasks.update({
          id: taskId,
          name: task.name,
          projectId: task.projectId,
          status: task.status,
          defaultTask: task.defaultTask,
          pinned: task.pinned,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new task
        await tx.tasks.insert({
          ...task,
          id: taskId,
          lastUsed: null,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })

        // Create task to task catalog relation
        await tx.taskToTaskCatalogs.insert({
          taskId: taskId,
          taskCatalogId,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    })
  } catch (error) {
    console.error('Error saving task:', error)
  }
}
