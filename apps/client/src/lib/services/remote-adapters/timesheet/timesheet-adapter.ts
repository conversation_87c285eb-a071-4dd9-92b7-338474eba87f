import type {
  ProjectCatalog,
  Schema,
  TaskCatalog,
  Timerecord,
  TimerecordToTaskCatalog,
} from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { invariant, maxBy, minBy } from 'es-toolkit'
import { ServiceType } from '~/lib/model/service-type-model'
import {
  formatDateAsIso,
  formatDateTimeFromTimestamp,
  formatOptDateTimeFromTimestamp,
} from '~/utils/date'
import type {
  ParsingResult,
  ProjectCatalogObject,
  RemoteAdapter,
  RemoteServiceObject,
  RemoteServiceProvider,
  RemoteWorklog,
  TaskCatalogObject,
  WorklogObject,
} from '../remote-adapter-types'
import { type TimecardArrayData, getTimecard } from './timesheet-client'

export class TimesheetAdapter implements RemoteAdapter {
  serviceType = ServiceType.GOFORE_TIMESHEET
  needsImportForFoundProject = false

  async addWorklog(
    z: Zero<Schema>,
    worklog: WorklogObject,
    remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('adding worklog')

    const dateOfWorklog = new Date(worklog.startTimestamp)
    const start = new Date(dateOfWorklog)
    start.setHours(0, 0, 0, 0)
    const end = new Date(dateOfWorklog)
    end.setHours(23, 59, 59, 999)

    console.log('find worklogs of the same day', {
      start: start.toISOString(),
      end: end.toISOString(),
    })

    // first find all worklogs which have a taskCatalog within the same remote service
    // and the same date (today)
    const worklogs = await z.query.timerecords
      .where('startTimestamp', '>=', start.getTime())
      .where('startTimestamp', '<=', end.getTime())
      .whereExists('timerecordToTaskCatalogs', (q) =>
        q.whereExists('taskCatalog', (q) =>
          q.whereExists('projectCatalog', (q) =>
            q.where('remoteServiceId', '=', remoteService.id)
          )
        )
      )
      .related('timerecordToTaskCatalogs', (q) =>
        q.related('taskCatalog', (q) => q.related('projectCatalog'))
      )
      .orderBy('startTimestamp', 'asc')
      .run()

    const dateStr = formatDateAsIso(dateOfWorklog)
    const timecards = await getTimecard(dateStr, remoteService)
    const timecard = calculateTimecard(worklogs, timecards, worklog)
    console.log('result', timecard)
    const summaries = calculateSummaries(
      worklogs as TimeRecordWithTaskCatalogs[]
    )
    console.log('summaries', summaries)
    return {} as { worklogId: string; timestamp: Date }
  }

  async getWorklog(
    _z: Zero<Schema>,
    _taskCatalog: TaskCatalogObject,
    _worklogId: string,
    _remoteService: RemoteServiceObject
  ): Promise<RemoteWorklog> {
    console.log('not implemented')
    return {} as RemoteWorklog
  }

  async updateWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteWorklog: RemoteWorklog | null,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('not implemented')
    return {} as { worklogId: string; timestamp: Date }
  }

  async updateAndMoveWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _oldRemoteIssue: TaskCatalogObject,
    _remoteService: RemoteServiceObject
  ): Promise<{ worklogId: string; timestamp: Date }> {
    console.log('not implemented')
    return {} as { worklogId: string; timestamp: Date }
  }

  async deleteWorklog(
    _z: Zero<Schema>,
    _worklog: WorklogObject,
    _remoteService: RemoteServiceObject
  ): Promise<number> {
    console.log('not implemented')
    return 0
  }

  async findAllProjects(
    _z: Zero<Schema>,
    _remoteService: RemoteServiceObject
  ): Promise<ProjectCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTasksForProjectByKeyStartingWith(
    _z: Zero<Schema>,
    _keyPrefix: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTasksForProjectBySearchText(
    _z: Zero<Schema>,
    _searchText: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findMostRecentTasksForProject(
    _z: Zero<Schema>,
    _projectCatalog: ProjectCatalogObject,
    _remoteService: RemoteServiceObject
  ): Promise<TaskCatalogObject[]> {
    console.log('not implemented')
    return []
  }

  async findTaskByKey(
    _z: Zero<Schema>,
    _issueKey: string,
    _projectCatalog: ProjectCatalogObject
  ): Promise<TaskCatalogObject | null> {
    console.log('not implemented')
    return null
  }

  async supportsUrl(
    _url: URL,
    _provider: RemoteServiceProvider
  ): Promise<boolean> {
    console.log('not implemented')
    return false
  }

  async parseIssueKeyFromUrl(
    _url: URL,
    _provider: RemoteServiceProvider
  ): Promise<ParsingResult | undefined> {
    console.log('not implemented')
    return undefined
  }

  async tryAuthenticate(
    _z: Zero<Schema>,
    _remoteService: RemoteServiceObject
  ): Promise<boolean> {
    console.log('not implemented')
    return false
  }
}

type TimecardResult = {
  start: string
  end: string
  breaks: { start: string; end: string }[]
}

export function calculateTimecard(
  worklogs: Timerecord[],
  timecards: TimecardArrayData,
  worklog: WorklogObject
): TimecardResult | undefined {
  const logWorklogs = worklogs.map((it) => ({
    ...it,
    startTimestamp: formatDateTimeFromTimestamp(it.startTimestamp),
    endTimestamp: formatOptDateTimeFromTimestamp(it.endTimestamp ?? undefined),
  }))

  console.log('worklog', JSON.stringify(worklog, null, 2))
  console.log('timecards', JSON.stringify(timecards, null, 2))
  console.log('worklogs of the same day', JSON.stringify(logWorklogs, null, 2))
  const startTime = minBy(worklogs, (it) => it.startTimestamp)?.startTimestamp
  const worklogsWithEndTime = worklogs.filter((it) => it.endTimestamp)
  const endTimeRecord = maxBy(worklogsWithEndTime, (it) => it.endTimestamp ?? 0)
  const endTime = endTimeRecord?.endTimestamp

  const breaks: { start: string; end: string }[] = []
  if (worklogs.length > 1) {
    worklogs.forEach((worklog, index) => {
      const nextWorklog = worklogs[index + 1]
      if (nextWorklog) {
        const pause = calculatePauseBetween(worklog, nextWorklog)
        if (pause > 0) {
          breaks.push({
            start: new Date(worklog.endTimestamp ?? 0).toISOString(),
            end: new Date(nextWorklog.startTimestamp).toISOString(),
          })
        }
      }
    })
  }

  invariant(startTime, 'No start time')
  invariant(endTime, 'No end time')

  return {
    start: new Date(startTime).toISOString(),
    end: new Date(endTime).toISOString(),
    breaks,
  }
}

function calculatePauseBetween(
  worklog: Timerecord,
  nextWorklog: Timerecord
): number {
  if (!worklog.endTimestamp || !nextWorklog.startTimestamp) {
    return 0
  }
  const pause = nextWorklog.startTimestamp - worklog.endTimestamp
  if (pause > TEN_MINUTES_MS) {
    return pause
  }
  return 0
}

export type TimeRecordWithTaskCatalogs = Timerecord & {
  timerecordToTaskCatalogs: Array<
    TimerecordToTaskCatalog & {
      taskCatalog: TaskCatalog & {
        projectCatalog: ProjectCatalog
      }
    }
  >
}

export function calculateSummaries(
  worklogs: TimeRecordWithTaskCatalogs[]
): unknown {
  console.log(`worklogs ${JSON.stringify(worklogs)}`)
  console.log('worklogs', worklogs)
  return null
}
const TEN_MINUTES_MS = 10 * 60 * 1000
