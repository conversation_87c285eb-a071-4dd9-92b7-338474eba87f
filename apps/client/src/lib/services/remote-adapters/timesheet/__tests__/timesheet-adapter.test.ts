import type { Schema } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { invariant } from 'es-toolkit'
import { describe, expect, it, vi } from 'vitest'
import { formatDateTime } from '~/utils/date'
import type { RemoteServiceObject } from '../../remote-adapter-types'
import {
  type TimeRecordWithTaskCatalogs,
  TimesheetAdapter,
  calculateSummaries,
  calculateTimecard,
} from '../timesheet-adapter'
import { getTimecard } from '../timesheet-client'

vi.mock('../timesheet-client')

const getTimecardMock = vi.mocked(getTimecard)
const mockZRun = vi.fn()

// Mock Zero instance
const mockZ = {
  query: {
    timerecords: {
      where: vi.fn().mockReturnThis(),
      whereExists: vi.fn().mockReturnThis(),
      related: vi.fn().mockReturnThis(),
      orderBy: vi.fn().mockReturnThis(),
      run: mockZRun,
    },
  },
} as unknown as Zero<Schema>

// Mock remote service
const mockRemoteService: RemoteServiceObject = {
  id: 'test-service-id',
  name: 'Test Service',
  serviceType: 'GOFORE_TIMESHEET',
  remoteUrl: 'https://test.example.com',
  remoteUser: '<EMAIL>',
  remotePassword: 'password',
}

const worklogsOfTheSameDay = [
  {
    id: '0196fc28-0aaa-777a-a378-e9b181904ba4',
    taskId: '0196c359-d044-70f1-b5c5-9a8cf6f90d43',
    startTimestamp: 1747989900000,
    endTimestamp: 1747993500000,
    comment: 'check logs and analyze issues for supreme natives',
    uploadSuccess: false,
    uploaded: null,
    createdAt: 1747987204778,
    updatedAt: 1748204860723,
    createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    uploadData: null,
  },
  {
    id: '0196fc8a-f7a4-7f10-aec2-3ae5ebb9c07d',
    taskId: '0196c359-d044-70f1-b5c5-9a8cf6f90d43',
    startTimestamp: 1747993500000,
    endTimestamp: 1747995300000,
    comment:
      'fix production problems and support Manfred fixing a broken article',
    uploadSuccess: false,
    uploaded: null,
    createdAt: 1747993687972,
    updatedAt: 1748204566921,
    createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    uploadData: null,
  },
  {
    id: '0196fca4-0070-7cd2-9582-fbc0e3fb0839',
    taskId: '0196e77b-f100-7bed-b7df-e3ce525a0898',
    startTimestamp: 1747997100000,
    endTimestamp: 1748012400000,
    comment: 'ÖBB Tender',
    uploadSuccess: false,
    uploaded: null,
    createdAt: 1747995328624,
    updatedAt: 1748206997659,
    createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    uploadData: null,
  },
]

const mockTimecards = [
  {
    timecardId: '35CFF8D4D2412BDFE0630300010A5C57',
    employeeId: 502014,
    editoriId: 502014,
    projectTaskId: null,
    startTime: '10:45:00',
    endTime: '17:00:00',
    date: '2025-05-23',
    comments: null,
    dateLastModified: '2025-05-23T15:08:59.841499Z',
    breaks: [
      {
        timecardId: '35CFF8D4D2412BDFE0630300010A5C57',
        breakId: '35CFFA2353D72E10E0630300010AA94B',
        startTime: '12:15:00',
        endTime: '12:45:00',
      },
    ],
    state: 'VALID',
    interval: {
      start: '2025-05-23T10:45:00',
      end: '2025-05-23T17:00:00',
    },
  },
]

const mockWorklog = {
  id: '0196fca4-0070-7cd2-9582-fbc0e3fb0839',
  taskId: '0196e77b-f100-7bed-b7df-e3ce525a0898',
  startTimestamp: 1747997100000,
  endTimestamp: 1748012400000,
  comment: 'ÖBB Tender',
  uploadSuccess: false,
  uploaded: null,
  createdAt: 1747995328624,
  updatedAt: 1748206754519,
  createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
  updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
  uploadData: null,
  task: {
    id: '0196e77b-f100-7bed-b7df-e3ce525a0898',
    name: 'Sales Support (gofore)',
    projectId: '0196a975-16b7-773a-a381-85c063b46811',
    status: 'OPEN',
    defaultTask: false,
    pinned: false,
    lastUsed: 1747995328624,
    createdAt: 1747640381696,
    updatedAt: 1747995328624,
    createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    project: {
      id: '0196a975-16b7-773a-a381-85c063b46811',
      name: 'Internal Duties',
      customerId: '0196a94c-d460-7c8c-b4dc-f580b05483e6',
      projectCatalogId: '0196a975-16b1-7439-9874-dd13a55c821e',
      color: '',
      timeNormalizationType: '',
      timeNormalizationConfig: '',
      createdAt: 1746599745207,
      updatedAt: 1746599745207,
      createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
      updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
    },
  },
}

describe('TimesheetAdapter', () => {
  it('should add worklog', async () => {
    getTimecardMock.mockResolvedValue(mockTimecards)
    mockZRun.mockResolvedValue(worklogsOfTheSameDay)

    const sut = new TimesheetAdapter()
    const result = await sut.addWorklog(mockZ, mockWorklog, mockRemoteService)
    expect(result).toBeDefined()
  })

  it('should calculate timecard', () => {
    const result = calculateTimecard(
      worklogsOfTheSameDay,
      mockTimecards,
      mockWorklog
    )
    invariant(result, 'No result')
    expect({
      breaks: result.breaks.map((it) => ({
        end: formatDateTime(new Date(it.end)),
        start: formatDateTime(new Date(it.start)),
      })),
      start: formatDateTime(new Date(result.start)),
      end: formatDateTime(new Date(result.end)),
    }).toEqual({
      breaks: [
        {
          start: '23.05.2025 12:15:00',
          end: '23.05.2025 12:45:00',
        },
      ],
      start: '23.05.2025 10:45:00',
      end: '23.05.2025 17:00:00',
    })
  })

  it('should calculate the summaries', () => {
    const mockWorklogs: TimeRecordWithTaskCatalogs[] = [
      {
        id: '01971732-31c1-7687-a3f4-b5ff954ca3f9',
        taskId: '0196c359-d044-70f1-b5c5-9a8cf6f90d43',
        startTimestamp: 1748419200000,
        endTimestamp: 1748440800000,
        comment: 'graphql madness',
        uploadSuccess: false,
        uploaded: null,
        createdAt: 1748440854977,
        updatedAt: 1748466899733,
        createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
        updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
        uploadData: null,
        timerecordToTaskCatalogs: [
          {
            timerecordId: '01971732-31c1-7687-a3f4-b5ff954ca3f9',
            taskCatalogId: '0196a975-1654-7e0c-9f69-97d8762e8458',
            createdAt: 1748466894661,
            updatedAt: 1748466894661,
            createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
            updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
            taskCatalog: {
              id: '0196a975-1654-7e0c-9f69-97d8762e8458',
              name: 'MBN',
              key: 'MBN',
              status: 'OPEN',
              remoteId: '18503',
              remoteUrl: '',
              projectCatalogId: '0196a975-164a-7284-a8cf-e89a1f80b85d',
              lastUsed: null,
              pinned: false,
              createdAt: 1746599745108,
              updatedAt: 1746599745108,
              createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              projectCatalog: {
                id: '0196a975-164a-7284-a8cf-e89a1f80b85d',
                name: 'RB MBN',
                key: 'PR5059',
                remoteId: '633433',
                remoteUrl: '',
                remoteServiceId: '01969ee4-1796-71c7-b1ee-c779279137f4',
                createdAt: 1746599745098,
                updatedAt: 1746599745098,
                createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
                updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              },
            },
          },
        ],
      },
      {
        id: '01971788-7c27-7d4e-9dea-dfffd3c0cdca',
        taskId: '0196c359-d044-70f1-b5c5-9a8cf6f90d43',
        startTimestamp: 1748446200000,
        endTimestamp: 1748460600000,
        comment: 'finalize graphql integration',
        uploadSuccess: false,
        uploaded: null,
        createdAt: 1748446510119,
        updatedAt: 1748467844406,
        createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
        updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
        uploadData: null,
        timerecordToTaskCatalogs: [
          {
            timerecordId: '01971788-7c27-7d4e-9dea-dfffd3c0cdca',
            taskCatalogId: '0196a975-1654-7e0c-9f69-97d8762e8458',
            createdAt: 1748460727049,
            updatedAt: 1748460727049,
            createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
            updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
            taskCatalog: {
              id: '0196a975-1654-7e0c-9f69-97d8762e8458',
              name: 'MBN',
              key: 'MBN',
              status: 'OPEN',
              remoteId: '18503',
              remoteUrl: '',
              projectCatalogId: '0196a975-164a-7284-a8cf-e89a1f80b85d',
              lastUsed: null,
              pinned: false,
              createdAt: 1746599745108,
              updatedAt: 1746599745108,
              createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              projectCatalog: {
                id: '0196a975-164a-7284-a8cf-e89a1f80b85d',
                name: 'RB MBN',
                key: 'PR5059',
                remoteId: '633433',
                remoteUrl: '',
                remoteServiceId: '01969ee4-1796-71c7-b1ee-c779279137f4',
                createdAt: 1746599745098,
                updatedAt: 1746599745098,
                createdBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
                updatedBy: 'c50b8068-23d8-4686-8f0b-e22bd19a5ecd',
              },
            },
          },
        ],
      },
    ]
    const result = calculateSummaries(mockWorklogs)
    expect(result).toBeNull()
  })
})
