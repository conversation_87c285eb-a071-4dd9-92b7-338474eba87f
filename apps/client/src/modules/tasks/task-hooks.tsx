import type { Schema, Task, TaskCatalog } from '@ftt/shared'
import type { Zero } from '@rocicorp/zero'
import { uuidv7 } from 'uuidv7'

// Custom hook for task management
export function useTaskManagement(z: Zero<Schema>, existingTasks: Task[]) {
  // Handle save
  const handleSave = async (task: Task) => {
    try {
      if (existingTasks.some((t) => t.id === task.id)) {
        // Update existing task
        await z.mutate.tasks.update({
          id: task.id,
          name: task.name,
          projectId: task.projectId,
          status: task.status,
          defaultTask: task.defaultTask,
          pinned: task.pinned,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new task
        await z.mutate.tasks.insert({
          ...task,
          id: task.id ?? uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving task:', error)
    }
  }

  // Handle delete
  const handleDelete = async (task: Task) => {
    try {
      await z.mutate.tasks.delete({
        id: task.id,
      })
    } catch (error) {
      console.error('Error deleting task:', error)
    }
  }

  return { handleSave, handleDelete }
}

// Custom hook for task catalog management
export function useTaskCatalogManagement(
  z: Zero<Schema>,
  existingTaskCatalogs: TaskCatalog[]
) {
  // Handle save
  const handleSave = async (taskCatalog: TaskCatalog) => {
    try {
      if (existingTaskCatalogs.some((tc) => tc.id === taskCatalog.id)) {
        // Update existing task catalog
        await z.mutate.taskCatalogs.update({
          id: taskCatalog.id,
          name: taskCatalog.name,
          key: taskCatalog.key,
          status: taskCatalog.status,
          remoteId: taskCatalog.remoteId,
          remoteUrl: taskCatalog.remoteUrl,
          projectCatalogId: taskCatalog.projectCatalogId,
          pinned: taskCatalog.pinned,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new task catalog
        await z.mutate.taskCatalogs.insert({
          ...taskCatalog,
          id: taskCatalog.id || uuidv7(),
          lastUsed: null,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving task catalog:', error)
    }
  }

  // Handle delete
  const handleDelete = async (taskCatalog: TaskCatalog) => {
    try {
      await z.mutate.taskCatalogs.delete({
        id: taskCatalog.id,
      })
    } catch (error) {
      console.error('Error deleting task catalog:', error)
    }
  }

  return { handleSave, handleDelete }
}
