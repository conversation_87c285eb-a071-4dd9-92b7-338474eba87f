// These data structures define your client-side schema.
// They must be equal to or a subset of the server-side schema.
// Note the "relationships" field, which defines first-class
// relationships between tables.
// See https://github.com/rocicorp/mono/blob/main/apps/zbugs/schema.ts
// for more complex examples, including many-to-many.

import {
  ANYONE_CAN,
  type ExpressionBuilder,
  type PermissionsConfig,
  type Row,
  definePermissions,
} from '@rocicorp/zero'

import { type Schema, schema } from './schema.gen'
export { schema, type Schema }

export type User = Row<typeof schema.tables.users>
export type Timer = Row<typeof schema.tables.timers>
export type Customer = Row<typeof schema.tables.customers>
export type Project = Row<typeof schema.tables.projects>
export type RemoteService = Row<typeof schema.tables.remoteServices>
export type ProjectCatalog = Row<typeof schema.tables.projectCatalogs>
export type TaskCatalog = Row<typeof schema.tables.taskCatalogs>
export type Task = Row<typeof schema.tables.tasks>
export type Timerecord = Row<typeof schema.tables.timerecords>
export type TimerecordToTaskCatalog = Row<
  typeof schema.tables.timerecordToTaskCatalogs
>
export type Query = Row<typeof schema.tables.queries>

// The contents of your decoded JWT.
type AuthData = {
  sub: string | null
}

export const permissions = definePermissions<AuthData, Schema>(schema, () => {
  const allowIfLoggedIn = (
    authData: AuthData,
    { cmpLit }: ExpressionBuilder<Schema, keyof Schema['tables']>
  ) => cmpLit(authData.sub, 'IS NOT', null)

  const allowIfTimerOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'timers'>
  ) => cmp('userId', '=', authData.sub ?? '')

  const allowIfCustomerOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'customers'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfProjectOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'projects'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfRemoteServiceOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'remoteServices'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfProjectCatalogOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'projectCatalogs'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfTaskCatalogOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'taskCatalogs'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfTaskOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'tasks'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfTimeRecordOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'timerecords'>
  ) => cmp('createdBy', '=', authData.sub ?? '')
  const allowIfQueryOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'queries'>
  ) => cmp('createdBy', '=', authData.sub ?? '')

  const allowIfAppStateOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'appState'>
  ) => cmp('id', '=', authData.sub ?? '')

  const allowIfTaskCatToTaskCreator = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'taskToTaskCatalogs'>
  ) => cmp('createdBy', '=', authData.sub ?? '')

  const allowIfTimeRecordToTaskCatCreator = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'timerecordToTaskCatalogs'>
  ) => cmp('createdBy', '=', authData.sub ?? '')

  const allowIfFailedSyncsOwner = (
    authData: AuthData,
    { cmp }: ExpressionBuilder<Schema, 'failedSyncs'>
  ) => cmp('userId', '=', authData.sub ?? '')

  return {
    users: {
      row: {
        select: ANYONE_CAN,
      },
    },
    timers: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTimerOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTimerOwner],
        },
        // must be logged in to delete
        delete: [allowIfTimerOwner],
        // everyone can read current messages
        select: [allowIfTimerOwner],
      },
    },

    customers: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfCustomerOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfCustomerOwner],
        },
        // must be logged in to delete
        delete: [allowIfCustomerOwner],
        // everyone can read current messages
        select: [allowIfCustomerOwner],
      },
    },

    projects: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfProjectOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfProjectOwner],
        },
        // must be logged in to delete
        delete: [allowIfProjectOwner],
        // everyone can read current messages
        select: [allowIfProjectOwner],
      },
    },

    remoteServices: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfRemoteServiceOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfRemoteServiceOwner],
        },
        // must be logged in to delete
        delete: [allowIfRemoteServiceOwner],
        // everyone can read current messages
        select: [allowIfRemoteServiceOwner],
      },
    },

    projectCatalogs: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfProjectCatalogOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfProjectCatalogOwner],
        },
        // must be logged in to delete
        delete: [allowIfProjectCatalogOwner],
        // everyone can read current messages
        select: [allowIfProjectCatalogOwner],
      },
    },

    taskCatalogs: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTaskCatalogOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTaskCatalogOwner],
        },
        // must be logged in to delete
        delete: [allowIfTaskCatalogOwner],
        // everyone can read current messages
        select: [allowIfTaskCatalogOwner],
      },
    },

    tasks: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTaskOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTaskOwner],
        },
        // must be logged in to delete
        delete: [allowIfTaskOwner],
        // everyone can read current messages
        select: [allowIfTaskOwner],
      },
    },

    taskToTaskCatalogs: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTaskCatToTaskCreator],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTaskCatToTaskCreator],
        },
        // must be logged in to delete
        delete: [allowIfTaskCatToTaskCreator],
        // everyone can read current messages
        select: [allowIfTaskCatToTaskCreator],
      },
    },

    timerecordToTaskCatalogs: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTimeRecordToTaskCatCreator],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTimeRecordToTaskCatCreator],
        },
        // must be logged in to delete
        delete: [allowIfTimeRecordToTaskCatCreator],
        // everyone can read current messages
        select: [allowIfTimeRecordToTaskCatCreator],
      },
    },

    timerecords: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfTimeRecordOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfTimeRecordOwner],
        },
        // must be logged in to delete
        delete: [allowIfTimeRecordOwner],
        // everyone can read current messages
        select: [allowIfTimeRecordOwner],
      },
    },

    queries: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          // owner can only edit own timers
          preMutation: [allowIfQueryOwner],
          // owner can only edit messages to be owned by self
          postMutation: [allowIfQueryOwner],
        },
        // must be logged in to delete
        delete: [allowIfQueryOwner],
        // everyone can read current messages
        select: [allowIfQueryOwner],
      },
    },

    appState: {
      row: {
        insert: [allowIfAppStateOwner],
        select: [allowIfAppStateOwner],
        update: {
          preMutation: [allowIfAppStateOwner],
          postMutation: [allowIfAppStateOwner],
        },
        delete: [allowIfAppStateOwner],
      },
    },

    failedSyncs: {
      row: {
        // anyone can insert
        insert: [allowIfLoggedIn],
        update: {
          preMutation: [allowIfFailedSyncsOwner],
          postMutation: [allowIfFailedSyncsOwner],
        },
        delete: [allowIfFailedSyncsOwner],
        select: [allowIfFailedSyncsOwner],
      },
    },
  } satisfies PermissionsConfig<AuthData, Schema>
})
